package cn.dutp.system.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import lombok.Data;

@Data
public class VisitDataVo {
    @Excel(name = "学校名称")
    private String schoolName;
    @Excel(name = "日期")
    private String logDate;
    @Excel(name = "独立访客数")
    private Integer userQuantity;
    @Excel(name = "系统访问量")
    private Integer totalQuantity;
    @Excel(name = "教师访问数")
    private Integer teacherQuantity;
    @Excel(name = "学生访问数")
    private Integer studentQuantity;
}
