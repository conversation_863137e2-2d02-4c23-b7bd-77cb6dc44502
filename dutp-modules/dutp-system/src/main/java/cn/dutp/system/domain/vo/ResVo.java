package cn.dutp.system.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
public class ResVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 1公开教材2校本教材
     */
    private Integer bookOrganize;

    /**
     * 0不需处理1未处理2通过3驳回
     */
    @Excel(name = "1未绑定2未兑换3已兑换4已过期")
    private Integer state;

}