package cn.dutp.system.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookManagementMyReviewVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 我审核过得教材总数
     */
    private Integer myReviewBookNumber;
    /**
     * 公开教材数量
     */
    private Integer openTextbooksNumber;
    /**
     * 校本教材数量
     */
    private Integer schoolBasedTextbooksNumber;



}