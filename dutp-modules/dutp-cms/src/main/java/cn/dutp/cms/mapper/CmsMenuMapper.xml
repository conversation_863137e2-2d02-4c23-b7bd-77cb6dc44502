<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.dutp.cms.mapper.CmsMenuMapper">
    
    <resultMap type="CmsMenu" id="CmsMenuResult">
        <result property="menuId"    column="menu_id"    />
        <result property="menuName"    column="menu_name"    />
        <result property="sort"    column="sort"    />
        <result property="parentId"    column="parent_id"    />
        <result property="menuRoute"    column="menu_route"    />
        <result property="routeType"    column="route_type"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCmsMenuVo">
        select menu_id, menu_name, sort, parent_id, menu_route, route_type, del_flag, create_by, create_time, update_by, update_time from cms_menu
    </sql>

    <select id="selectCmsMenuList" parameterType="CmsMenu" resultMap="CmsMenuResult">
        <include refid="selectCmsMenuVo"/>
        <where>  
            <if test="menuName != null  and menuName != ''"> and menu_name like concat('%', #{menuName}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="menuRoute != null  and menuRoute != ''"> and menu_route = #{menuRoute}</if>
            <if test="routeType != null "> and route_type = #{routeType}</if>
        </where>
    </select>
    
    <select id="selectCmsMenuByMenuId" parameterType="Long" resultMap="CmsMenuResult">
        <include refid="selectCmsMenuVo"/>
        where menu_id = #{menuId}
    </select>

    <insert id="insertCmsMenu" parameterType="CmsMenu" useGeneratedKeys="true" keyProperty="menuId">
        insert into cms_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="menuName != null">menu_name,</if>
            <if test="sort != null">sort,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="menuRoute != null">menu_route,</if>
            <if test="routeType != null">route_type,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="menuName != null">#{menuName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="menuRoute != null">#{menuRoute},</if>
            <if test="routeType != null">#{routeType},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCmsMenu" parameterType="CmsMenu">
        update cms_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="menuName != null">menu_name = #{menuName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="menuRoute != null">menu_route = #{menuRoute},</if>
            <if test="routeType != null">route_type = #{routeType},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where menu_id = #{menuId}
    </update>

    <delete id="deleteCmsMenuByMenuId" parameterType="Long">
        delete from cms_menu where menu_id = #{menuId}
    </delete>

    <delete id="deleteCmsMenuByMenuIds" parameterType="String">
        delete from cms_menu where menu_id in 
        <foreach item="menuId" collection="array" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>
</mapper>