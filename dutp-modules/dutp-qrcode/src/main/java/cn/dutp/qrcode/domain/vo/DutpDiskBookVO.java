package cn.dutp.qrcode.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 智典云盘书籍对象 dutp_disk_book
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Data
public class DutpDiskBookVO {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * $column.columnComment
     */
    @Excel(name = "扫描教材名称")
    private String bookName;

    /**
     * ISBN
     */
    @Excel(name = "ISBN")
    private String isbn;

    /**
     * issn
     *
    private String issn;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /** 二维码id */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long qrcodeId;

    /** 二维码图片 */
    @Excel(name = "二维码图片")
    private String imageUrl;

    /** 二维码名称 */
    @Excel(name = "二维码名称")
    private String qrcodeName;

    /** 二维码数量 */
    @Excel(name = "二维码数量")
    private Integer qrCodeCount;

    /** 扫描数量 */
    @Excel(name = "扫描数量")
    private Integer scanningCount;

    /**
     * 二维码删除标志（0代表存在 2代表删除）
     */
    private String qrcodeDelFlag;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("createTime")
    @Excel(name = "扫码时间")
    private Date createTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("bookId", getBookId())
                .append("bookName", getBookName())
                .append("isbn", getIsbn())
                .append("delFlag", getDelFlag())
                .append("createTime", getCreateTime())
                .append("qrcodeId", getQrcodeId())
                .append("qrcodeName", getQrcodeName())
                .append("qrcodeDelFlag", getQrcodeDelFlag())
                .append("qrCodeCount", getQrCodeCount())
                .append("scanningCount", getScanningCount())
                .toString();
    }
}
