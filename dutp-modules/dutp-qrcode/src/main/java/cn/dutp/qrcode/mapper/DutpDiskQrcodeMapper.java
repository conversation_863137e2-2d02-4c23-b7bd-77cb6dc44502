package cn.dutp.qrcode.mapper;

import java.util.List;

import cn.dutp.qrcode.domain.dto.DutpDiskStatisticsDto;
import cn.dutp.qrcode.domain.vo.DutpDiskBookVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import cn.dutp.qrcode.domain.DutpDiskQrcode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
/**
 * 智典云盘资源Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Repository
public interface DutpDiskQrcodeMapper extends BaseMapper<DutpDiskQrcode>
{

    List<DutpDiskBookVO> getStatisticsDetailList(DutpDiskStatisticsDto dto);

    List<DutpDiskQrcode> selectDutpDiskQrcodeListForDownload(@Param("qrcodeIds") List<Long> qrcodeIds, @Param("bookIds") List<Long> bookIds, @Param("qrcodeType") String qrcodeType, @Param("state") String state);

    List<DutpDiskQrcode> selectDutpDiskQrcodeSearchList(DutpDiskQrcode dutpDiskQrcode);

    List<DutpDiskQrcode> getQrcodeList(@Param("bookIds") List<Long> bookIds);
}
