package cn.dutp.qrcode.service.impl;

import java.util.List;

import cn.dutp.qrcode.domain.DutpDiskUserTemplate;
import cn.dutp.qrcode.mapper.DutpDiskUserTemplateMapper;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.dutp.qrcode.mapper.DutpDiskQrcodeTemplateMapper;
import cn.dutp.qrcode.domain.DutpDiskQrcodeTemplate;
import cn.dutp.qrcode.service.IDutpDiskQrcodeTemplateService;

/**
 * 二维码模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-02
 */
@Service
public class DutpDiskQrcodeTemplateServiceImpl extends ServiceImpl<DutpDiskQrcodeTemplateMapper, DutpDiskQrcodeTemplate> implements IDutpDiskQrcodeTemplateService
{
    @Autowired
    private DutpDiskQrcodeTemplateMapper dutpDiskQrcodeTemplateMapper;

    @Autowired
    private DutpDiskUserTemplateMapper dutpDiskUserTemplateMapper;

    /**
     * 查询二维码模板
     *
     * @param templateId 二维码模板主键
     * @return 二维码模板
     */
    @Override
    public DutpDiskQrcodeTemplate selectDutpDiskQrcodeTemplateByTemplateId(Long templateId)
    {
        return this.getById(templateId);
    }

    /**
     * 查询二维码模板列表
     *
     * @param dutpDiskQrcodeTemplate 二维码模板
     * @return 二维码模板
     */
    @Override
    public List<DutpDiskQrcodeTemplate> selectDutpDiskQrcodeTemplateList(DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate)
    {
        LambdaQueryWrapper<DutpDiskQrcodeTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                if(ObjectUtil.isNotEmpty(dutpDiskQrcodeTemplate.getImageUrl())) {
                lambdaQueryWrapper.eq(DutpDiskQrcodeTemplate::getImageUrl
                ,dutpDiskQrcodeTemplate.getImageUrl());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcodeTemplate.getTemplateName())) {
                lambdaQueryWrapper.like(DutpDiskQrcodeTemplate::getTemplateName
                ,dutpDiskQrcodeTemplate.getTemplateName());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcodeTemplate.getComponentPath())) {
                lambdaQueryWrapper.eq(DutpDiskQrcodeTemplate::getComponentPath
                ,dutpDiskQrcodeTemplate.getComponentPath());
            }
                if(ObjectUtil.isNotEmpty(dutpDiskQrcodeTemplate.getDefaultData())) {
                lambdaQueryWrapper.eq(DutpDiskQrcodeTemplate::getDefaultData
                ,dutpDiskQrcodeTemplate.getDefaultData());
            }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 新增二维码模板
     *
     * @param dutpDiskQrcodeTemplate 二维码模板
     * @return 结果
     */
    @Override
    public boolean insertDutpDiskQrcodeTemplate(DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate)
    {
        return this.save(dutpDiskQrcodeTemplate);
    }

    /**
     * 修改二维码模板
     *
     * @param dutpDiskQrcodeTemplate 二维码模板
     * @return 结果
     */
    @Override
    public boolean updateDutpDiskQrcodeTemplate(DutpDiskQrcodeTemplate dutpDiskQrcodeTemplate)
    {
        return this.updateById(dutpDiskQrcodeTemplate);
    }

    /**
     * 批量删除二维码模板
     *
     * @param templateIds 需要删除的二维码模板主键
     * @return 结果
     */
    @Override
    public boolean deleteDutpDiskQrcodeTemplateByTemplateIds(List<Long> templateIds)
    {
        return this.removeByIds(templateIds);
    }

    @Override
    public DutpDiskQrcodeTemplate selectDutpDiskUserTemplateByUserId(Long userId) {

        //查询这个用户的默认的模板


        DutpDiskUserTemplate userTemplate = dutpDiskUserTemplateMapper.selectOne(new LambdaQueryWrapper<DutpDiskUserTemplate>()
                .eq(DutpDiskUserTemplate::getUserId, userId)
                .eq(DutpDiskUserTemplate::getIsDefault, 1)
        );

        if(ObjectUtil.isNotEmpty(userTemplate)){
            return this.getById(userTemplate.getTemplateId());
        }
        return null;
    }
}
