package cn.dutp.book.uitls;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 文本改写
 */
public class TextRewritingUtil {
    public static String doRequest(String textRewritingUrl,String appid,String apiKey,String apiSecret,String level,String text) throws Exception {
        URL realUrl = new URL(buildRequetUrl(textRewritingUrl,apiKey,apiSecret));
        URLConnection connection = realUrl.openConnection();
        HttpURLConnection httpURLConnection = (HttpURLConnection)connection;
        httpURLConnection.setDoInput(true);
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setRequestMethod("POST");
        httpURLConnection.setRequestProperty("Content-type", "application/json");
        OutputStream out = httpURLConnection.getOutputStream();
        String params = buildParam(appid,text,level);
        out.write(params.getBytes(StandardCharsets.UTF_8));
        out.flush();

        InputStream is;
        try {
            is = httpURLConnection.getInputStream();
        } catch (Exception var8) {
            is = httpURLConnection.getErrorStream();
            throw new Exception("make request error:code is " + httpURLConnection.getResponseMessage() + readAllBytes(is));
        }

        return readAllBytes(is);
    }

    public static String buildRequetUrl(String textRewritingUrl,String apiKey,String apiSecret) {
        String httpRequestUrl = textRewritingUrl.replace("ws://", "http://").replace("wss://", "https://");

        try {
            URL url = new URL(httpRequestUrl);
            SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("GMT"));
            String date = format.format(new Date());
            String host = url.getHost();
            if (url.getPort() != 80 && url.getPort() != 443) {
                host = host + ":" + url.getPort();
            }

            String builder = "host: " + host + "\ndate: " + date + "\nPOST " + url.getPath() + " HTTP/1.1";
            Charset charset = StandardCharsets.UTF_8;
            Mac mac = Mac.getInstance("hmacsha256");
            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "hmacsha256");
            mac.init(spec);
            byte[] hexDigits = mac.doFinal(builder.getBytes(charset));
            String sha = Base64.getEncoder().encodeToString(hexDigits);
            String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"", apiKey, "hmac-sha256", "host date request-line", sha);
            String authBase = Base64.getEncoder().encodeToString(authorization.getBytes(charset));
            return String.format("%s?authorization=%s&host=%s&date=%s", textRewritingUrl, URLEncoder.encode(authBase), URLEncoder.encode(host), URLEncoder.encode(date));
        } catch (Exception var14) {
            Exception e = var14;
            throw new RuntimeException("assemble requestUrl error:" + e.getMessage());
        }
    }

    private static String buildParam(String appId,String text,String level) throws UnsupportedEncodingException {
        String param = "{    \"header\": {        \"app_id\": \"" + appId + "\",        \"status\": 3    },    \"parameter\": {        \"se3acbe7f\": {            \"level\": \"" + level + "\",            \"result\": {                \"encoding\": \"utf8\",                \"compress\": \"raw\",                \"format\": \"json\"            }        }    },    \"payload\": {        \"input1\": {            \"encoding\": \"utf8\",            \"compress\": \"raw\",            \"format\": \"plain\",            \"status\": 3,            \"text\": \"" + Base64.getEncoder().encodeToString(text.getBytes(StandardCharsets.UTF_8)) + "\"        }    }}";
        return param;
    }

    private static String readAllBytes(InputStream is) throws IOException {
        byte[] b = new byte[1024];
        StringBuilder sb = new StringBuilder();

        int len;
        while((len = is.read(b)) != -1) {
            sb.append(new String(b, 0, len, StandardCharsets.UTF_8));
        }

        return sb.toString();
    }

    class Result {
        public String compress;
        public String encoding;
        public String format;
        public String text;

        Result() {
        }
    }

    class Payload {
        public TextRewritingUtil.Result result;

        Payload() {
        }
    }

    class Header {
        public int code;
        public String message;
        public String sid;

        Header() {
        }
    }

    class JsonParse {
        public TextRewritingUtil.Header header;
        public TextRewritingUtil.Payload payload;

        JsonParse() {
        }
    }

}
