package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * DUTP-DTB_020划线对象 dtb_user_book_line
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class DtbUserBookLineVO
{
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lineId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long chapterId;
    @Excel(name = "页码")
    private Long pageNumber;
    @Excel(name = "划线文字")
    private String word;
    @Excel(name = "颜色色值")
    private String color;
    private String fromWordId;
    private String endWordId;
    private String lineStyle;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date createTime;
}
