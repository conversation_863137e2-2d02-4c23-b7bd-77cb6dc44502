package cn.dutp.book.domain.vo;

import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.util.List;

/**
 * 数字教材作者编辑团队对象
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class DtbBookGroupVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 书稿联系人
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long contact;

    /**
     * 主编
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> editor;

    /**
     * 副主编
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> associateEditor;

    /**
     * 参编
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> participateCompilation;

    /**
     * 责任编辑
     */
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> editorInCharge;

    /**
     * 策划编辑
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long planningEditor;
}
