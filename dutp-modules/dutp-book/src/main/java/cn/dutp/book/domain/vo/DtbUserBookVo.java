package cn.dutp.book.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * DUTP-DTB_014学生/教师书架对象 dtb_user_book
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Data
public class DtbUserBookVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;
    /**  */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userBookId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /** 教材ID */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /** 版本ID */
    @Excel(name = "版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long versionId;

    /** 中图分类ID */
    @Excel(name = "中图分类ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookTypeId;

    /** 来源1购买2校本教材3试用 */
    @Excel(name = "来源1购买2校本教材3试用")
    private Integer addWay;

    /** 字体 */
    @Excel(name = "字体")
    private String fontFamily;

    /** 字号 */
    @Excel(name = "字号")
    private Integer fontSize;

    /** 栏目数 */
    @Excel(name = "栏目数")
    private Integer columnQuantity;

    /** 行间距 */
    @Excel(name = "行间距")
    private BigDecimal lineHeight;

    /** 阅读百分比 */
    @Excel(name = "阅读百分比")
    private Integer readRate;

    /** 阅读模式1专注模式2滚屏模式 */
    @Excel(name = "阅读模式1专注模式2滚屏模式")
    private Integer readMode;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /** 最后的查看时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "最后的查看时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date lastSeeDate;

    /** 排序 */
    @Excel(name = "排序")
    private Long sort;

    /** 书名 */
    @Excel(name = "书名")
    private String bookName;

    /** 封面 */
    @Excel(name = "封面")
    private String cover;

    /**
     * 上架状态1已上架2未上架
     */
    @Excel(name = "上架状态1已上架2未上架")
    private Integer shelfState;

    /**
     * 当前版本ID
     */
    @Excel(name = "当前版本ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long currentVersionId;

    /**
     * 最新的版本ID 如果跟当前版本ID一直，无修正
     */
    @Excel(name = "最新的版本ID 如果跟当前版本ID一直，无修正")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastVersionId;



    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("userBookId", getUserBookId())
                .append("userId", getUserId())
                .append("bookId", getBookId())
                .append("versionId", getVersionId())
                .append("bookTypeId", getBookTypeId())
                .append("addWay", getAddWay())
                .append("fontFamily", getFontFamily())
                .append("fontSize", getFontSize())
                .append("columnQuantity", getColumnQuantity())
                .append("lineHeight", getLineHeight())
                .append("readRate", getReadRate())
                .append("readMode", getReadMode())
                .append("expireDate", getExpireDate())
                .append("lastSeeDate", getLastSeeDate())
                .append("sort", getSort())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("bookName", getBookName())
                .append("cover", getCover())
                .append("shelfState", getShelfState())
                .append("currentVersionId", getCurrentVersionId())
                .append("lastVersionId", getLastVersionId())
                .toString();
    }
}
