package cn.dutp.common.encrypt.utils;


import cn.dutp.common.core.exception.UtilException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.NotNull;
import java.security.MessageDigest;

/**
 * 加密工具类，包含MD5加密算法
 * <AUTHOR>
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Md5Utils {

    /**
     * MD5加密函数
     * 将输入的字符串进行MD5加密
     * @param input 需要加密的字符串
     * @return 加密后的MD5字符串
     */
    public static String md5(@NotNull String input) {
        //非空判断
        if (Strings.isEmpty(input)) {
            throw new UtilException("输入字符串为空，无法生成哈希值");
        }

        try {
            // 获取MD5算法的消息摘要对象
            MessageDigest md = MessageDigest.getInstance("MD5");
            // 计算输入字符串的消息摘要
            byte[] messageDigest = md.digest(input.getBytes());
            // 构建最终的十六进制字符串结果
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                // 将每个字节转换为十六进制字符串
                String hex = Integer.toHexString(0xff & b);
                // 确保每个字节对应的十六进制字符串占两位，不足两位前补0
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            // 返回最终的MD5加密结果
            return hexString.toString();
        } catch (Exception e) {
            // 如果发生异常，抛出运行时异常
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        String key = md5("dutp@!2025");
        System.out.println(key);
    }
}
