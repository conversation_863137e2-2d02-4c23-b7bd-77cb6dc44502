package cn.dutp.common.pay.configure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @author: wangjiaxin
 */
@Data
@ConfigurationProperties(prefix = "pay.alipay")
public class AliPayProperties {

    /**
     * APPID 即创建应用后生成
     */
    private String appId;

    /**
     * 开发者私钥，由开发者自己生成。
     */
    private String privateKey;

    /**
     * 商户证书序列号
     */
    private String charset;

    /**
     * 生成签名字符串所使用的签名算法类型，目前支持 RSA2 和 RSA，推荐使用 RSA2。
     */
    private String signType;

    /**
     * 支付宝公钥，由支付宝生成。
     */
    private String alipayPublicKey;

    /**
     * alipay_cert_path	支付宝公钥证书文件本地路径。
     */
    private String alipayCertPath;

    /**
     * 支付回调地址
     */
    private String notifyUrl;
}
