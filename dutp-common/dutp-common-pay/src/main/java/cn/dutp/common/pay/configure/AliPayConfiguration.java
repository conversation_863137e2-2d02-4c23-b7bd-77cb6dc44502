package cn.dutp.common.pay.configure;



import cn.dutp.common.pay.beans.AliPayKey;
import cn.dutp.common.pay.configure.properties.AliPayProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 读取yml中的支付配置
 * @author: wangjiaxin
 */

@Configuration
@EnableConfigurationProperties(AliPayProperties.class)
public class AliPayConfiguration {

    @Bean
    public AliPayKey alPayKey(AliPayProperties aliPayProperties) {
        AliPayKey aliPayKey = new AliPayKey();

        aliPayKey.setAppId(aliPayProperties.getAppId());
        aliPayKey.setAlipayPublicKey(aliPayProperties.getAlipayPublicKey());
        aliPayKey.setPrivateKey(aliPayProperties.getPrivateKey());
        aliPayKey.setCharset(aliPayProperties.getCharset());
        aliPayKey.setSignType(aliPayProperties.getSignType());
        aliPayKey.setAlipayCertPath(aliPayProperties.getAlipayCertPath());
        aliPayKey.setNotifyUrl(aliPayProperties.getNotifyUrl());

        return aliPayKey;
    }

}

