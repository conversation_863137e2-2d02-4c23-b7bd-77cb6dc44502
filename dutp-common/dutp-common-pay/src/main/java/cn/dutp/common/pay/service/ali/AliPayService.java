package cn.dutp.common.pay.service.ali;

import cn.dutp.common.core.exception.ServiceException;
import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.pay.beans.AliPayKey;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.domain.AlipayTradePrecreateModel;
import com.alipay.api.domain.AlipayTradeQueryModel;
import com.alipay.api.domain.AlipayTradeRefundApplyModel;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.request.AlipayTradePrecreateRequest;
import com.alipay.api.request.AlipayTradeQueryRequest;
import com.alipay.api.request.AlipayTradeRefundApplyRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.alipay.api.response.AlipayTradeRefundApplyResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * 支付宝支付服务类
 * <AUTHOR>
 *
 */
@Service
public class AliPayService {


    private static final AliPayKey ALI_PAY_KEY = SpringUtils.getBean(AliPayKey.class);

    private final static Logger log = LoggerFactory.getLogger(AliPayService.class);

    static AlipayClient alipayClient ;
    @PostConstruct
    public void init() {
        //实例化客户端
        alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", ALI_PAY_KEY.getAppId(), ALI_PAY_KEY.getPrivateKey(), "json", ALI_PAY_KEY.getCharset(), ALI_PAY_KEY.getAlipayPublicKey(), ALI_PAY_KEY.getSignType());
    }

    /**
     * 支付接口,返回支付二维码，需要前端或后端把字符串转换为二维码图片
     * @return
     */
    public String payQrCode() throws AlipayApiException {
        // 2、设置请求参数
        AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
        request.setNotifyUrl(ALI_PAY_KEY.getNotifyUrl());
        //同步回调地址,即支付成功后前端页面跳转的地址，这里不需要
        //request.setReturnUrl("");
        //商品明细信息，按需传入
        JSONObject json = new JSONObject();
        //支付参数
        AlipayTradePrecreateModel model = new AlipayTradePrecreateModel();
        //订单编号
        model.setOutTradeNo("20250313");
        //订单标题
        model.setSubject("订单标题");
        //订单金额，精确到小数点后两位
        model.setTotalAmount("0.01");
        //订单描述
        model.setBody("订单描述");
        //超时时间参数
        model.setTimeoutExpress("2025-02-13 15:00:00");
        //产品编号
        model.setStoreId("0912743172309");
        request.setBizModel(model);
        //3.通过alipayClient调用API，获得对应的response类
        AlipayTradePrecreateResponse response = alipayClient.execute(request);
        if (response.isSuccess()) {
            return response.getQrCode();
        }
        return response.getMsg();
    };

/**
 * 创建支付宝预支付订单
 * 该方法用于生成支付宝APP支付所需的订单信息，包括订单号、支付金额、订单标题、订单描述、超时时间等
 * 并调用支付宝的支付接口，返回支付响应对象
 *
 * @param outTradeNo 由商家自定义，64个字符以内，仅支持字母、数字、下划线且需保证在商户端不重复。
 * @param totalFee 订单总金额，单位为元，精确到小数点后两位
 * @param subject 订单标题，用于在支付宝支付页面显示,不可使用特殊字符，如 /，=，& 等。
 * @param timeExpire 订单支付超时时间，格式为yyyy-MM-dd HH:mm:ss，超过该时间后订单自动关闭
 * @param body 订单描述，对订单的描述，可以是商品名称等信息
 * @param productCode 支付宝销售产品码，商家和支付宝签约的产品码
 * @return AlipayTradeAppPayResponse 支付宝支付响应对象，包含支付所需的信息
 */
public AlipayTradeAppPayResponse prepay(String outTradeNo,String totalFee,String subject,String timeExpire,String body,String productCode){

   //实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.trade.app.pay
    AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
    //SDK已经封装掉了公共参数，这里只需要传入业务参数。以下方法为sdk的model入参方式(model和biz_content同时存在的情况下取biz_content)。
    AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
    model.setBody(body);
    model.setSubject(subject);
    model.setOutTradeNo(outTradeNo);
    model.setTimeoutExpress(timeExpire);
    model.setTotalAmount(totalFee);
    model.setProductCode(productCode);
    request.setBizModel(model);
    //设置支付宝服务器主动通知商户服务器里指定的页面http/https路径
    request.setNotifyUrl(ALI_PAY_KEY.getNotifyUrl());
    try {
        AlipayTradeAppPayResponse response = alipayClient.execute(request);
        if (response.isSuccess()) {
            log.info("支付成功");
            return response;
        } else {
            log.error("支付失败, 错误码: {}, 错误信息: {}", response.getCode(), response.getMsg());
            throw new ServiceException("支付失败：" + response.getSubMsg());
        }
    } catch (AlipayApiException e) {
        // 捕获并处理非 JSON 响应
        if (e.getCause() instanceof IOException) {
            String errorHtml = e.getMessage();
            if (errorHtml.contains("invalid-app-id")) {
                log.error("无效的 AppId: {}", ALI_PAY_KEY.getAppId());
                throw new ServiceException("无效的 AppId");
            }
        }
        log.error("支付宝调起支付异常: {}", e.getMessage());
        throw new ServiceException("支付宝调起支付异常: " + e.getMessage());
    }
}


/**
 * 查询支付订单状态
 * 该方法用于向支付宝发起请求，查询特定交易号或商户订单号对应的订单状态
 *
 * @param outTradeNo 商户订单号，用于标识一笔交易在商户系统的唯一编号
 * @param tradeNo 支付宝交易号，用于标识一笔交易在支付宝系统的唯一编号
 *                这两个参数只要其中一个就可以完成查询，如果两个都提供，则优先使用tradeNo
 */
public void queryOrder(String outTradeNo,String tradeNo){
    // 创建支付宝交易查询请求对象
    AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
    // 创建交易查询模型对象，用于设置查询所需的业务参数
    AlipayTradeQueryModel model = new AlipayTradeQueryModel();
    // 设置商户订单号
    model.setOutTradeNo(outTradeNo);
    // 设置支付宝交易号
    model.setTradeNo(tradeNo);
    // 将查询模型对象设置到请求对象中
    request.setBizModel(model);
    // 尝试执行请求，向支付宝发送查询订单的请求
    try {
        log.info("支付宝查询订单参数:{}", JSON.toJSONString(model));
        alipayClient.execute(request);
    } catch (AlipayApiException e) {
        log.error("支付宝支付订单查询异常:{}", e.getMessage());
        // 如果查询过程中发生异常，则抛出自定义的服务异常，包含异常信息
        throw new ServiceException("支付宝支付订单查询异常:"+e.getMessage());
    }
}


/**
 * 关闭支付宝支付订单
 *
 * @param outTradeNo 商户订单号，用于标识一笔交易
 * @param tradeNo 支付宝交易号，可选参数，用于标识支付宝侧的一笔交易
 *                这两个参数只要其中一个就可以完成查询，如果两个都提供，则优先使用tradeNo
 * <p>
 * 本方法通过调用支付宝的交易查询接口来关闭订单
 * 选择使用商户订单号或支付宝交易号来查询并关闭订单
 * 如果订单不存在或已关闭，则不执行任何操作
 * 如果调用过程中出现异常，则抛出自定义的运行时异常
 */
public void closeOrder(String outTradeNo,String tradeNo){
    // 创建支付宝交易查询请求对象
    AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
    // 创建交易查询模型对象，并设置商户订单号和支付宝交易号
    AlipayTradeQueryModel model = new AlipayTradeQueryModel();
    model.setOutTradeNo(outTradeNo);
    model.setTradeNo(tradeNo);
    request.setBizModel(model);
    try {
        log.info("支付宝关闭订单参数:{}", JSON.toJSONString(model));
        // 执行交易查询请求，以关闭订单
        alipayClient.execute(request);
    } catch (AlipayApiException e) {
        log.error("支付宝支付订单关闭异常:{}", e.getMessage());
        // 处理支付宝API调用异常，抛出自定义的运行时异常
        throw new ServiceException("支付宝支付订单关闭异常:"+e.getMessage());
    }
}


/**
 * 发起支付宝退款申请
 *
 * @param outTradeNo 商户订单号，用于标识一笔交易
 * @param tradeNo 支付宝交易号，用于标识一笔交易
 *                这两个参数只要其中一个就可以完成查询，如果两个都提供，则优先使用tradeNo
 * @param refundReason 退款原因，说明退款的具体原因
 * @param outRequestNo 商户退款请求号，用于标识一笔退款请求
 */
public AlipayTradeRefundApplyResponse refund(String outTradeNo,String tradeNo,String refundReason,String outRequestNo){
    // 创建支付宝退款申请请求对象
    AlipayTradeRefundApplyRequest request = new AlipayTradeRefundApplyRequest();
    // 创建退款申请模型对象，用于设置退款申请的相关信息
    AlipayTradeRefundApplyModel model = new AlipayTradeRefundApplyModel();
    // 设置商户订单号
    model.setOutTradeNo(outTradeNo);
    // 设置支付宝交易号
    model.setTradeNo(tradeNo);
    // 设置退款原因
    model.setRefundReason(refundReason);
    // 设置商户退款请求号
    model.setOutRequestNo(outRequestNo);
    // 将退款申请模型对象设置到请求对象中
    request.setBizModel(model);

    // 执行退款申请
    try {
        log.info("支付宝退款参数:{}", JSON.toJSONString(model));
        return alipayClient.execute(request);
    } catch (AlipayApiException e) {
        log.error("支付宝退款异常:{}", e.getMessage());
        // 如果退款申请过程中出现异常，抛出自定义的服务异常
        throw new ServiceException("支付宝退款异常:"+e.getMessage());

    }
}


    /**
     * 查询退款状态
     * 该方法通过支付宝的API查询特定交易的退款状态，需要提供交易的外部交易号或支付宝交易号
     *
     * @param outTradeNo 商家订单号，用于标识一笔交易
     * @param tradeNo 支付宝交易号，用于标识一笔交易
     *                这两个参数只要其中一个就可以完成查询，如果两个都提供，则优先使用tradeNo
     */
    public void queryRefund(String outTradeNo,String tradeNo){
        // 创建支付宝交易查询请求对象
        AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
        // 创建交易查询模型对象，用于设置查询参数
        AlipayTradeQueryModel model = new AlipayTradeQueryModel();
        // 设置查询模型中的外部交易号
        model.setOutTradeNo(outTradeNo);
        // 设置查询模型中的支付宝交易号
        model.setTradeNo(tradeNo);
        // 将查询模型设置到请求对象中
        request.setBizModel(model);
        try {
            log.info("支付宝退款查询参数:{}", JSON.toJSONString(model));
            // 执行请求，查询交易状态
            alipayClient.execute(request);
        }catch (AlipayApiException e) {
            log.error("支付宝退款查询异常:{}", e.getMessage());
            // 如果发生支付宝API异常，抛出自定义的服务异常
            throw new ServiceException("支付宝退款异常:"+e.getMessage());
        }
    }

}
