package cn.dutp.common.pay.service.wechat;

import cn.dutp.common.core.utils.SpringUtils;
import cn.dutp.common.pay.beans.WechatPayKey;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.partnerpayments.nativepay.model.Amount;
import com.wechat.pay.java.service.partnerpayments.nativepay.NativePayService;
import com.wechat.pay.java.service.partnerpayments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.partnerpayments.nativepay.model.PrepayResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;


import javax.annotation.PostConstruct;

/**
 * 微信支付服务
 * <AUTHOR>
 * 此为微信支付native版本接口接口，需在微信商户平台配置相关参数
 * 主要用于微信扫码，其他接口请使用WechatPayService
 */
@Service
public class WechatPayNativeService {

    private static final WechatPayKey WECHAT_PAY_KEY = SpringUtils.getBean(WechatPayKey.class);

    public static NativePayService service;

    private final static Logger log = LoggerFactory.getLogger(WechatPayNativeService.class);

    @PostConstruct
    public void init() {
        // 初始化商户配置
        Config config = new RSAAutoCertificateConfig.Builder()
                        .merchantId(WECHAT_PAY_KEY.getMerchantId())
                        // 使用 com.wechat.pay.java.core.util 中的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
                        .privateKeyFromPath(WECHAT_PAY_KEY.getPrivateKeyPath())
                        .merchantSerialNumber(WECHAT_PAY_KEY.getMerchantSerialNumber())
                        .apiV3Key(WECHAT_PAY_KEY.getApiV3Key())
                        .build();
        // 初始化服务
        service = new NativePayService.Builder().config(config).build();
    }


    /** Native支付预下单 */
    public static PrepayResponse prepay(String description,String outTradeNo,int totalFee) {
        PrepayRequest request = new PrepayRequest();
        request.setSpAppid(WECHAT_PAY_KEY.getAppId());
        request.setSpMchid(WECHAT_PAY_KEY.getMerchantId());
        request.setDescription(description);
        request.setOutTradeNo(outTradeNo);

        Amount amount = new Amount();
        amount.setTotal(totalFee);
        request.setAmount(amount);

        log.info("微信native支付参数:{}", request);
        // 调用接口
        return service.prepay(request);
    }

}
