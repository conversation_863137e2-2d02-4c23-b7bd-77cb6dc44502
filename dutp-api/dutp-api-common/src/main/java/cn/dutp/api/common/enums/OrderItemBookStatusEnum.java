package cn.dutp.api.common.enums;

/**
 * 订单明细书籍状态
 */
public enum OrderItemBookStatusEnum {
    NORMAL("normal", "正常"),
    CANCELING("canceling", "申请作废"),
    CANCEL("cancel", "已作废");

    private final String code;
    private final String description;

    OrderItemBookStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OrderItemBookStatusEnum fromCode(String code) {
        for (OrderItemBookStatusEnum status : OrderItemBookStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OrderStatus code: " + code);
    }
}

