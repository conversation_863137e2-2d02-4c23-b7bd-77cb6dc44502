package cn.dutp.api.common.enums;

/**
 * 订单明细状态
 */
public enum OrderItemStatusEnum {
    NORMAL("normal", "正常"),
    CANCELING("canceling", "申请作废"),
    CANCEL("cancel", "已作废");

    private final String code;
    private final String description;

    OrderItemStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static OrderItemStatusEnum fromCode(String code) {
        for (OrderItemStatusEnum status : OrderItemStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown OrderItemStatusEnum code: " + code);
    }
}
