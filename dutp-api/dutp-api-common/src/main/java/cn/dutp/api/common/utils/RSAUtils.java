package cn.dutp.api.common.utils;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;

/**
 * <AUTHOR>
 */
@Data
@Component
public class RSAUtils {

    @Value("${rsa.public.key}")
    private String publicKeyString;

    @Value("${rsa.private.key}")
    private String privateKeyString;

    private PublicKey publicKey;
    private PrivateKey privateKey;

    /**
     * 初始化方法，用于加载公钥和私钥
     * 该方法在类实例化后调用，主要用于准备必要的密钥资源
     *
     * @throws Exception 如果密钥加载过程中发生错误，则抛出异常
     */
    @PostConstruct
    public void init() throws Exception {
        // 检查公钥和私钥字符串是否已配置
        if (publicKeyString == null || privateKeyString == null) {
            // 如果公钥或私钥未配置，则抛出非法参数异常
            throw new IllegalArgumentException("Public or Private key is not configured properly.");
        }
        // 加载公钥
        this.publicKey = loadPublicKey(publicKeyString);
        // 加载私钥
        this.privateKey = loadPrivateKey(privateKeyString);
    }

    /**
     * 加载公钥并返回 PublicKey 对象
     *
     * @param key 公钥字符串，包含 PEM 格式的头尾标识符和换行符
     * @return 返回加载后的 PublicKey 对象
     * @throws Exception 如果公钥加载过程中发生错误，则抛出异常
     */
    private PublicKey loadPublicKey(String key) throws Exception {
        // 去除 PEM 格式的头尾标识符和换行符
        key = key.replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\n", "");

        // 将 Base64 编码的字符串解码为字节数组
        byte[] decodedKey = Base64.getDecoder().decode(key);

        // 使用 RSA 算法生成公钥对象
        return KeyFactory.getInstance("RSA").generatePublic(new java.security.spec.X509EncodedKeySpec(decodedKey));
    }

    /**
     * 加载私钥并返回 PrivateKey 对象
     *
     * @param key 私钥字符串，包含 PEM 格式的头尾标识符和换行符
     * @return 返回加载后的 PrivateKey 对象
     * @throws Exception 如果私钥加载过程中发生错误，则抛出异常
     */
    private PrivateKey loadPrivateKey(String key) throws Exception {
        // 去除 PEM 格式的头尾标识符和换行符
        key = key.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\n", "");

        // 将 Base64 编码的字符串解码为字节数组
        byte[] decodedKey = Base64.getDecoder().decode(key);

        // 使用 RSA 算法生成私钥对象
        return KeyFactory.getInstance("RSA").generatePrivate(new java.security.spec.PKCS8EncodedKeySpec(decodedKey));
    }

    /**
     * 使用公钥加密数据
     *
     * @param data 要加密的原始数据（字符串形式）
     * @return 加密后的数据（Base64 编码）
     * @throws Exception 异常
     */
    public String encrypt(String data) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);

        // 将原始数据转换为字节数组
        byte[] encryptedBytes = cipher.doFinal(data.getBytes());

        // 将加密后的字节数组转换为 Base64 编码字符串返回
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * 使用私钥解密数据
     *
     * @param encryptedData 加密后的数据（Base64 编码）
     * @return 解密后的数据（字符串形式）
     * @throws Exception 异常
     */
    public String decrypt(String encryptedData) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);

        byte[] decodedData = Base64.getDecoder().decode(encryptedData);
        byte[] decryptedBytes = cipher.doFinal(decodedData);

        // 返回解密后的数据（字符串形式）
        return new String(decryptedBytes);
    }

    public static void main(String[] args) {
        RSAUtils rsaUtils = new RSAUtils();
         String privateString = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDT" +
                "EKw0Ile2N8BgpUjIaN4XnF3gY0L5nDh6jd8eMnrfBRNVoWOIxYl4x4YWHepzbihZUXj1AL6/6" +
                "sicJJvhiMgAz2smIJ6kYoXimisA+rtIHmIYrL8Cy88DF0BtIxM8FKZxJpJGMLlJxCDjDHNGDT" +
                "juUJOfiVC8kF6fMysTMNia1IA6f37PlAk6QRBgRH93CfAiJtexApU+dgIbUROv0sCUxYjgW9a" +
                "SxdzKwG8MOdok4pF/Mqsny9NoXqV7RvDORGVUrQuvyMfw7bils5T8c7JCXyy+hq9zaayT7S3N" +
                "U0PLEbTuhfDWwjt/fSnJDX1tGehxJ3QaREAbEvFTbz0ejn2hAgMBAAECggEAA/c6wRZ4ju6Kp" +
                "cqXbMwFQ1taTks6EuhCR66sPAZbpxL1lvkwEsltBdmwPkIYgO7GAUNxd/DWwccQ5ZH8P+jAro5" +
                "Oa+Lz4ZY8aObdndnMjroi36wKtY79jgJKLMQ9bPxF47GarfG3SSzsVhWb9juEO3DQRxZb2HhB3" +
                "z9OCVJus3V30h0LdjcHKy5wAJjLHkLOPT1NBjCW5XPlWXZOXG9Kbi4wVL8oQQYlGd0bWlbc2wo" +
                "5rk8uQaQWcKKD4AHXqs5ywstN1LvMknZE4W6mbsR72Ejp3q309G0jCO7il6G65NA+/JUiaT/Js" +
                "2cco2ckhfMZuLyjP5FwLB+9nlFx7k/jVQKBgQDopHNgvteM6memhbxIQxttiqcDa8bVBMhVzkB" +
                "xiu9aEXfkWKPRl82jXCgwkofVf20fC37tKkjf3GGXHIBP45nkPkWSM09N3YwKRrsxhKBuHju2N" +
                "pga+cRUp+tq3TDWJXspW+DrKvqpXoCtMcAcMzeM9P5RZyVfKMboypfw0Bh0xQKBgQDoQaCI3qs" +
                "o8dgZfXTGc1xGOfwzMhj4zBlgNlzP3ASB7ZWI0V0VdMoJOgxhMhnxxFaNYaYQZr08MEEe/N+KM" +
                "SbZhm130x8AlPsbnIZPae65DOF8QdJyxepE2hBsGt7QeSskd/KGq8CbdW0Z15oNTotLNCdwCpd" +
                "/ferF1I3Ikx+LLQKBgQCwbY7GbtR/HIFQqxlX3/ScBUuVb5XRlJftYsZClW/haV/3/zT1TV95J" +
                "PmhpG54/fMeWRR//dJYWwRzmfzBGUVq78LJFjptPbfnTy7FZeRYvnrgO9TsmEBJRzNkzqwrqy8" +
                "U9YXEMsr3ySBiuFkNydW2A1IfH88atIzsnD/QM7BoVQKBgDHsQVyKOFJBwcig3eP2UgRKXHqIr" +
                "zNzc5TpuoN3cISZFAZnslF1XCQllRmjUk5UfKJBNhfabZytZqIJiudCb25cZdVWzRgr7dneiEQ" +
                "zqfue2C0EXP8MqbGJCfSixfgbw3lASyJFcs43rG7cmCF6G5jbqgp8TFo8L6W8bYmKjkI1AoGBA" +
                "N579jbm84eUwhD0COZF9QCfXCIcE0SEfog4to1YMlM4arYaHqys04sPt/vqdbckjkbB3pCnphB" +
                "J7halA5LFR3RImRjbPYZOcfv1PQRPM0xKxuNIev/o87FqKsBEriUg/dCQpg1sALuyD0UCInE2E" +
                "FJZLPr3kvFO/yTxCwtQlX80";
        String publicString = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0xCsNC" +
                "JXtjfAYKVIyGjeF5xd4GNC+Zw4eo3fHjJ63wUTVaFjiMWJeMeGFh3qc24oWVF49QC+v+rI" +
                "nCSb4YjIAM9rJiCepGKF4porAPq7SB5iGKy/AsvPAxdAbSMTPBSmcSaSRjC5ScQg4wxzRg" +
                "047lCTn4lQvJBenzMrEzDYmtSAOn9+z5QJOkEQYER/dwnwIibXsQKVPnYCG1ETr9LAlMWI" +
                "4FvWksXcysBvDDnaJOKRfzKrJ8vTaF6le0bwzkRlVK0Lr8jH8O24pbOU/HOyQl8svoavc2" +
                "msk+0tzVNDyxG07oXw1sI7f30pyQ19bRnocSd0GkRAGxLxU289Ho59oQIDAQAB";
        // 加载公钥
        try {
            rsaUtils.setPublicKey(rsaUtils.loadPublicKey(publicString));
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 加载私钥
        try {
            rsaUtils.setPrivateKey(rsaUtils.loadPrivateKey(privateString));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String value = null;
        try {
            value = rsaUtils.decrypt("FIjm8HOZgFOhbZazbm/2oQ+z0yNL/douwteQFossCCp+2Wah/LVkSu2/SyhioPUliDrKg2ofAYre2jzzyPK4J20UVJLwFJlkaJoV1H/I5CWtN9PA4deO4dI11S14rePCP3j8eD5aT9mETY51OxHtprJF/tLilShN6WoGrKoqvYvHwvinIshuGcNMAuQ22FrwG3QpPxY/KYmFCRE1fWPQA38WqvxB9afV6cN+Lh6Lb7ZWP8CrVwF6x8XqR12ZSyDKHeJzzjR8CKgtWNClZG/Hwz29RgXyBfsAuG6Mjd6vN4ndltfHsRCK3EDg41Ak2tiPQ36tG9qe20ZbnNleaowFOw==");
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("value====" + value);
    }
}
