package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 模型指令对象 dutp_ai_prompt
 *
 * <AUTHOR>
 * @date 2024-12-17
 */
@Data
@TableName("dutp_ai_prompt")
public class DutpAiPrompt extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long promptId;

    /**
     * 指令
     */
    @Excel(name = "指令")
    private String prompt;

    /**
     * 功能1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译
     */
    @Excel(name = "功能1续写2缩写3扩写4润色5生成试题6生成总结7生成学习目标8生成脑图9生成大纲10匹配案例11文生语音12文生图片13百度图片增强14百度图像修复15百度视频生成16文字纠错17文字纠错(上传黑名单 (违禁词替换) 和白名单 (免审词设置))18视频合规19文本合规20文本合规(上传黑白名单)21图片合规22音频合规23翻译")
    private Integer ability;

    /**
     * 讯飞大模型类型，lite指向Lite版本，generalv3指向Pro版本,pro-128k指向Pro-128K版本,generalv3.5指向Max版本,max-32k指向Max-32K版本,4.0Ultra指向4.0 Ultra版本;
     */
    @Excel(name = "讯飞大模型类型，lite指向Lite版本，generalv3指向Pro版本,pro-128k指向Pro-128K版本,generalv3.5指向Max版本,max-32k指向Max-32K版本,4.0Ultra指向4.0 Ultra版本;")
    private String xfType;

    /**
     * 模型类型1讯飞2百度
     */
    @Excel(name = "模型类型1讯飞2百度")
    private String modelType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("promptId", getPromptId())
                .append("prompt", getPrompt())
                .append("ability", getAbility())
                .append("xfType", getXfType())
                .append("modelType", getModelType())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
