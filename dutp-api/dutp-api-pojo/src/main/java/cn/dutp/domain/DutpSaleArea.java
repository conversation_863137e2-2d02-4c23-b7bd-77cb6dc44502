package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.serialize.LongListToStringSerializer;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 销售大区对象 dutp_sale_area
 *
 * <AUTHOR>
 * @date 2025-01-06
 */
@Data
@TableName("dutp_sale_area")
public class DutpSaleArea extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long areaId;

    /**
     * 大区名称
     */
    @Excel(name = "大区名称")
    private String areaName;

    /**
     * 大区负责人
     */
    @Excel(name = "大区负责人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long managerId;

    /**
     * 负责人名称
     */
    @TableField(exist = false)
    private String nickName;

    /**
     * 联系电话
     */
    @TableField(exist = false)
    private String phonenumber;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    @TableField(exist = false)
    private List<DutpSaleAreaMember> dutpSaleAreaMemberList;

    @TableField(exist = false)
    @JsonSerialize(using = LongListToStringSerializer.class)
    private List<Long> userIds;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("areaId", getAreaId())
                .append("areaName", getAreaName())
                .append("managerId", getManagerId())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
