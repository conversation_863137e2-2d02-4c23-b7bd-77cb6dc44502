package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * DUTP-DTB-031教材学校关系对象 dtb_book_school
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("dtb_book_school")
public class DtbBookSchool extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 1未推送2已推送，作废
     */
    @Excel(name = "1未推送2已推送，作废")
    private Integer sendFlag;

    /**
     * 推送次数
     */
    @Excel(name = "推送次数")
    private Integer sendQuantity;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;

    /**
     * 推送后的失效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送后的失效日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expireDate;

    /**
     * 推送人ID
     */
    @Excel(name = "推送人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sendMan;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("schoolId", getSchoolId())
                .append("bookId", getBookId())
                .append("sendFlag", getSendFlag())
                .append("sendQuantity", getSendQuantity())
                .append("sendTime", getSendTime())
                .append("expireDate", getExpireDate())
                .append("sendMan", getSendMan())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
