package cn.dutp.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtbEduBookOrderDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ISBN序列号
     */
    private String isbn;

    /**
     * 教材名称
     */
    private String bookName;

    /**
     * 书籍数量
     */
    private Integer bookQuantity;
    /**
     * 采购类型：1零售2教务采购3代采订单4样书订单5书商采购
     */
    private Integer orderType;
}