package cn.dutp.domain.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB-031教材学校关系对象 dtb_book_school
 *
 * <AUTHOR>
 * @date 2025-02-19
 */
@Data
@TableName("dtb_book_school")
public class DtbBookSchoolVo {
    // id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;
    // 学校名称
    private String schoolName;
}
