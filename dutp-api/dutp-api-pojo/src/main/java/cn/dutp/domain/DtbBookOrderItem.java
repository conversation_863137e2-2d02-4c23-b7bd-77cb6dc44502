package cn.dutp.domain;

import java.math.BigDecimal;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB-031订单对应的书籍明细对象 dtb_book_order_item
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@TableName("dtb_book_order_item")
public class DtbBookOrderItem extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderItemId;

    /**
     * 订单ID
     */
    @Excel(name = "订单ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 书籍ID
     */
    @Excel(name = "书籍ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 采购学校
     */
    @Excel(name = "采购学校")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 书的售价
     */
    @Excel(name = "书的售价")
    private BigDecimal priceSale;

    /**
     * 书的定价
     */
    @Excel(name = "书的定价")
    private BigDecimal priceCounter;

    /**
     * normal正常canceling申请作废cancel已作废
     */
    @Excel(name = "normal正常canceling申请作废cancel已作废")
    private String itemStatus;

    /**
     * 改完的单价
     */
    @Excel(name = "改完的单价")
    private BigDecimal priceOrderItem;

    /**
     * 书籍数量
     */
    @Excel(name = "书籍数量")
    private Long bookQuantity;

    /**
     * 折扣
     */
    @Excel(name = "折扣")
    private BigDecimal discount;

    /**
     * 教材名称
     */
    @TableField(exist = false)
    private String bookName;

    /**
     * ISBN序列号
     */
    @TableField(exist = false)
    private String isbn;

    /**
     * ISSN序列号
     */
    @TableField(exist = false)
    private String issn;

    /**
     * 教材编号
     */
    @TableField(exist = false)
    private String bookNo;

    /**
     * 1其他2主教材3副教材
     */
    @TableField(exist = false)
    private Integer masterFlag;

    /** 主教材ID */
    @TableField(exist = false)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long masterBookId;

    /**
     * 封面图片地址
     */
    @TableField(exist = false)
    private String cover;

    /**
     * 已激活的二维码数量
     */
    @TableField(exist = false)
    private Integer activationCodeCount;

    /**
     * 能否申请售后
     */
    @TableField(exist = false)
    private Boolean isRefund;

    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    @TableField(exist = false)
    private Integer shelfState;

    /**
     * 改完的单价的总和
     */
    @TableField(exist = false)
    private BigDecimal priceItemAmount;

    /**
     *  订单编号
     *  dtb_book_order.order_no
     */
    @TableField(exist = false)
    private String orderNo;

    /**
     *  订单类型: 1零售2教务采购3代采订单4样书订单5书商采购
     *  dtb_book_order.order_type
     */
    @TableField(exist = false)
    private Integer orderType;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("orderItemId", getOrderItemId())
                .append("orderId", getOrderId())
                .append("bookId", getBookId())
                .append("schoolId", getSchoolId())
                .append("priceSale", getPriceSale())
                .append("priceCounter", getPriceCounter())
                .append("itemStatus", getItemStatus())
                .append("priceOrderItem", getPriceOrderItem())
                .append("bookQuantity", getBookQuantity())
                .append("discount", getDiscount())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("cover", getCover())
                .append("activationCodeCount", getActivationCodeCount())
                .toString();
    }
}
