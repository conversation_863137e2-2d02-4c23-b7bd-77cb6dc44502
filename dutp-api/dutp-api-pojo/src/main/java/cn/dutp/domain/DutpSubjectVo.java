package cn.dutp.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.List;

/**
 * 学科信息对象 dutp_subject
 *
 * <AUTHOR>
 * @date 2024-11-12
 */
@Data
public class DutpSubjectVo {
    private static final long serialVersionUID = 1L;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long subjectId;

    /**
     * 学科名称
     */
    private String subjectName;

    /**
     * 父类id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 子类的嵌套
     */
    @TableField(exist = false)
    private List<DutpSubjectVo> children;
}
