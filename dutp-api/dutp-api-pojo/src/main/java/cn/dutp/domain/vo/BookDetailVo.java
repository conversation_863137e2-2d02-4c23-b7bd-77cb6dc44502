package cn.dutp.domain.vo;

import cn.dutp.domain.DtbBookGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 教材管理
 *
 * <AUTHOR>
 * @date
 */
@Data
public class BookDetailVo {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 教材名称
     */
    private String bookName;

    /**
     * 作者的标题
     */
    private String authorLabel;

    /**
     * 作者的值，作者字段分成两部分。
     */
    private String authorValue;

    /**
     * 封面图片地址
     */
    private String cover;

    /**
     * 顶级分类ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long topSubjectId;

    /**
     * 二级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long secondSubjectId;

    /**
     * 三级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long thirdSubjectId;

    /**
     * 四级ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long forthSubjectId;

    /**
     * ISBN序列号
     */
    private String isbn;

    /**
     * ISSN序列号
     */
    private String issn;

    /**
     * 教材编号
     */
    private String bookNo;

    /**
     * 出版日期
     */
    private Date publishDate;

    /**
     * 当前版本ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long currentVersionId;

    /**
     * 最新的版本ID 如果跟当前版本ID一致无修正
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long lastVersionId;

    /**
     * 上架时间
     */
    private Date shelfTime;

    /**
     * 下架时间
     */
    private Date unshelfTime;

    /**
     * 出版单位【弃用=出版社ID】
     */
    private String publishOrganization;

    /**
     * 出版状态1未出版2已出版
     */
    private Integer publishStatus;

    /**
     * 上架状态1已上架2未上架3召回4即将上架
     */
    private Integer shelfState;

    /**
     * 弃用，见dtb_book_school表
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 出版社ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long houseId;

    /**
     * 教材中图分类,关联dtb_book_type
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookType;

    /**
     * 1其他2主教材3副教材
     */
    private Integer masterFlag;

    /**
     * 主教材ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long masterBookId;

    /**
     * 销量
     */
    private Integer soldQuantity;

    /**
     * 阅读量
     */
    private Integer readQuantity;

    /**
     * 定价
     */
    private Double priceCounter;

    /**
     * 售价
     */
    private Double priceSale;

    /**
     * 1公开教材2校本教材
     */
    private Integer bookOrganize;

    /**
     * 选题号
     */
    private String topicNo;

    /**
     * 语种ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long languageId;

    /**
     * 教材当前节点
     */
    private Integer currentStepId;

    /**
     * 版次
     */
    private String edition;

    /**
     * 表标号样式1级,2级
     */
    private Integer tableNumberType;

    /**
     * 图号
     */
    private Integer imageNumberType;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 教师使用人数
     */
    private Integer studentNum;

    /**
     * 学生使用人数
     */
    private Integer teacherNum;

    /**
     * 主编
     */
    private String editor;

    /**
     * 作者信息
     */
    List<DtbBookGroup> groupList;

    /**
     * 书架信息
     */


    /**
     * 数字教材简介-编辑推荐
     */

    /**
     * 数字教材简介-教材简介
     */

    /**
     * 数字教材简介-版权信息
     */

    /**
     * 配套教材
     */


    /**
     * 图书审核节点名称
     */
    private String stepName;
}
