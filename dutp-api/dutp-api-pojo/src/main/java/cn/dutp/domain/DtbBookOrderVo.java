package cn.dutp.domain;

import cn.dutp.domain.vo.DtbEduBookOrderVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * DUTP-DTB_012订单表
 *
 * @TableName dtb_book_order
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DtbBookOrderVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单总数
     */
    private Integer totalOrderNumber;
    /**
     * 教材订购单： 5书商采购
     */
    private Integer sellerOrderNumber;
    /**
     * 教材订购单： 2和3管理端协助教务采购(代采订单）
     */
    private Integer proxyOrderNumber;
    /**
     * 订单购买的书籍
     */
    List<DtbEduBookOrderVo> orderBookList;

}