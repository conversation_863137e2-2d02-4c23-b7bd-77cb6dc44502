package cn.dutp.domain.vo;

import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 购书码导出记录对象 dtb_book_code_export_info
 *
 * <AUTHOR>
 * @date 2025-02-21
 */
@Data
@TableName("dtb_book_code_export_info")
public class DtbBookCodeExportInfoVo {
    /**
     * $column.columnComment
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;
    // 教材名称
    private String bookName;
    /**
     * 导出人
     */
    @Excel(name = "导出人")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long exportUserId;

    /**
     * 导出日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date exportDate;

    /**
     * 导出购书码数量
     */
    private Integer codeQuantity;
    // 导出人
    private String nickName;
    // 用户类型
    private String userType;
    // 手机号
    private String phonenumber;
    // 邮箱
    private String email;
}
