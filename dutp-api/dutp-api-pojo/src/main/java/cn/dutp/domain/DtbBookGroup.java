package cn.dutp.domain;

import cn.dutp.common.core.annotation.Excel;
import cn.dutp.common.core.web.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数字教材作者编辑团队对象 dtb_book_group
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
@TableName("dtb_book_group")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DtbBookGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;
    /**
     * $column.columnComment
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long groupId;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 1书稿联系人2主编3副主编4参编5策划编辑6责任编辑
     */
    @Excel(name = "1书稿联系人2主编3副主编4参编5策划编辑6责任编辑")
    private Integer roleType;

    /**
     * 用户ID sys_user表中user_id
     */
    @Excel(name = "用户ID sys_user表中user_id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
