package cn.dutp.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * 购书码发行管理对象 dtb_book_purchase_code
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@Data
public class DtbBookPurchaseCodeDto {
    // 购书码id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long codeId;
    // 订单下的购书码id
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderCodeId;
}
