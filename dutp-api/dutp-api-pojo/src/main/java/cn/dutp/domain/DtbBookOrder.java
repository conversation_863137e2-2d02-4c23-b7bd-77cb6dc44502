package cn.dutp.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.dutp.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.dutp.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * DUTP-DTB_012订单对象 dtb_book_order
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName("dtb_book_order")
public class DtbBookOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderId;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    private String orderNo;

    /**
     * 1零售2教务采购3代采订单4样书订单5书商采购
     */
    @Excel(name = "1零售2教务采购3代采订单4样书订单5书商采购")
    private Integer orderType;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long bookId;

    /**
     * 购书者ID，dutp_user表中userId
     */
    @Excel(name = "购书者ID，dutp_user表中userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userId;

    /**
     * 教务购买的用户ID=sys_user的userId
     */
    @Excel(name = "教务购买的用户ID=sys_user的userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolUserId;

    /**
     * 书商ID
     */
    @Excel(name = "书商ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long merchantId;

    /**
     * 创建者ID，后台创建者IDsys_user里的userId
     */
    @Excel(name = "创建者ID，后台创建者IDsys_user里的userId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long createUserId;

    /**
     * 采购学校,单独购买0
     */
    @Excel(name = "采购学校,单独购买0")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long schoolId;

    /**
     * 支付状态，N：待支付、A：已支付、F：支付失败
     */
    @Excel(name = "支付状态，N：待支付、A：已支付、F：支付失败")
    private String paymentStatus;

    /**
     * 支付方式，如信用卡、支付宝、微信支付等
     */
    @Excel(name = "支付方式，如信用卡、支付宝、微信支付等")
    private String paymentMethod;

    /**
     * 订单状态，pending：订单待确认[待支付]、paid已支付、editing：修改待确认、canceling作废待确认，create_refused采购驳回，edit_refused修改驳回, cancel_refused作废驳回，settlement结算中，completed：已完成（已结算）、first_refused一级审核驳回【样书】，second_refused二级审核驳回【样书】，second_auditting二级审核中【样书】，cancelled：已取消
     */
    @Excel(name = "订单状态，pending：订单待确认[待支付]、paid已支付、editing：修改待确认、canceling作废待确认，create_refused采购驳回，edit_refused修改驳回, cancel_refused作废驳回，settlement结算中，completed：已完成", readConverterExp = "已=结算")
    private String orderStatus;

    /**
     * 当前订单审核人ID
     */
    @Excel(name = "当前订单审核人ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long currentAuditUserId;

    /**
     * 0未退款1退款中2部分退款3全额退款
     */
    @Excel(name = "0未退款1退款中2部分退款3全额退款")
    private Integer refundStatus;

    /**
     * 发票状态0未申请1申请开票2已开票
     */
    @Excel(name = "发票状态0未申请1申请开票2已开票")
    private Integer invoiceStatus;

    /**
     * 付款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "付款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    private BigDecimal payAmount;

    /**
     * 支付凭证URL，废弃
     */
    @Excel(name = "支付凭证URL，废弃")
    private String payCertImageUrl;

    /**
     * 订单审核状态0待审核1通过2驳回【教务订单用，废弃】
     */
    @Excel(name = "订单审核状态0待审核1通过2驳回【教务订单用，废弃】")
    private Integer orderAuditStatus;

    /**
     * 0待审核1通过2驳回，支付凭证审核【教务订单用，废弃】
     */
    @Excel(name = "0待审核1通过2驳回，支付凭证审核【教务订单用，废弃】")
    private Integer paymentAuditStatus;

    /**
     * 原价
     */
    @Excel(name = "原价")
    private BigDecimal price;

    /**
     * 折扣
     */
    @Excel(name = "折扣")
    private BigDecimal discount;

    /**
     * 销售大区ID dutp_sale_area
     */
    @Excel(name = "销售大区ID dutp_sale_area")
    @JsonFormat(shape = JsonFormat.Shape.STRING)

    private Long areaId;

    /**
     * 经办人ID，大区下的人员
     */
    @Excel(name = "经办人ID，大区下的人员")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long operatorId;

    /**
     * 是否删除1未删除2用户删除3管理员删除
     */
    @Excel(name = "是否删除1未删除2用户删除3管理员删除")
    private Integer deleted;

    /**
     * 订单购买的书籍
     */
    @TableField(exist = false)
    List<DtbBookOrderItem> bookIdList;

    /**
     * 订单申请售后的书籍
     */
    @TableField(exist = false)
    List<DtbBookOrderItem> refundBookList;

    /**
     * 书的售价
     */
    @Excel(name = "书的售价")
    @TableField(exist = false)
    private BigDecimal priceSale;

    /**
     * 书的定价
     */
    @Excel(name = "书的定价")
    @TableField(exist = false)
    private BigDecimal priceCounter;

    /**
     * 修改订单类型，1订单详情2修改订单3作废订单4修改申请5取消订单6取消申请
     */
    @TableField(exist = false)
    Integer state;

    /**
     * 作废的书籍数据
     */
    @TableField(exist = false)
    private List<DtbBookOrderItem> cancelList;

    /** 申请售后备注备注 */
    @TableField(exist = false)
    private String refundRemark;

    private String remark;

    /**
     * 学校名称(样书用)
     */
    @TableField(exist = false)
    private String schoolName;

    /**
     * 用户名称(样书用)
     */
    @TableField(exist = false)
    private String userName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("orderId", getOrderId())
                .append("orderNo", getOrderNo())
                .append("orderType", getOrderType())
                .append("bookId", getBookId())
                .append("userId", getUserId())
                .append("schoolUserId", getSchoolUserId())
                .append("merchantId", getMerchantId())
                .append("createUserId", getCreateUserId())
                .append("schoolId", getSchoolId())
                .append("paymentStatus", getPaymentStatus())
                .append("paymentMethod", getPaymentMethod())
                .append("orderStatus", getOrderStatus())
                .append("currentAuditUserId", getCurrentAuditUserId())
                .append("refundStatus", getRefundStatus())
                .append("invoiceStatus", getInvoiceStatus())
                .append("payTime", getPayTime())
                .append("payAmount", getPayAmount())
                .append("payCertImageUrl", getPayCertImageUrl())
                .append("orderAuditStatus", getOrderAuditStatus())
                .append("paymentAuditStatus", getPaymentAuditStatus())
                .append("price", getPrice())
                .append("discount", getDiscount())
                .append("areaId", getAreaId())
                .append("operatorId", getOperatorId())
                .append("remark", getRemark())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("deleted", getDeleted())
                .append("priceSale", getPriceSale())
                .append("priceCounter", getPriceCounter())
                .toString();
    }
}
